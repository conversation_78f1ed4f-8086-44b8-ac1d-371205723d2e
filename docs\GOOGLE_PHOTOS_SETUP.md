# Configuration Google Photos API - Résolution Erreur 403

## Problème Actuel
L'erreur 403 "Permissions insuffisantes" indique que votre application n'est **pas correctement configurée** dans Google Cloud Console.

## Étapes de Configuration Obligatoires

### 1. Accéder à Google Cloud Console
1. Allez sur [Google Cloud Console](https://console.cloud.google.com/)
2. Connectez-vous avec votre compte Google
3. Sélectionnez votre projet `florasynth-a461d` (ou créez-en un nouveau)

### 2. Activer l'API Google Photos
1. Dans le menu de gauche, allez dans **"APIs & Services" > "Library"**
2. Recherchez **"Photos Library API"**
3. Cliquez sur l'API et appuyez sur **"ENABLE"**
4. ⚠️ **IMPORTANT** : Attendez que l'activation soit complète (peut prendre quelques minutes)

### 3. Configurer l'Écran de Consentement OAuth
1. Allez dans **"APIs & Services" > "OAuth consent screen"**
2. <PERSON><PERSON><PERSON>z **"External"** (sauf si vous avez un domaine Google Workspace)
3. Remplissez les champs obligatoires :
   - **App name** : "FloraSynth"
   - **User support email** : Votre email
   - **Developer contact information** : Votre email
4. Dans **"Scopes"**, ajoutez :
   - `https://www.googleapis.com/auth/photoslibrary.readonly`
5. Dans **"Test users"**, ajoutez votre email Google
6. **Sauvegardez**

### 4. Créer les Identifiants OAuth 2.0
1. Allez dans **"APIs & Services" > "Credentials"**
2. Cliquez sur **"+ CREATE CREDENTIALS" > "OAuth 2.0 Client IDs"**
3. Type d'application : **"Web application"**
4. Nom : "FloraSynth Web Client"
5. **Origines JavaScript autorisées** :
   - `http://localhost:5173` (pour le développement)
   - `https://votre-domaine-netlify.netlify.app` (pour la production)
6. **URI de redirection autorisés** :
   - `http://localhost:5173`
   - `https://votre-domaine-netlify.netlify.app`
7. **Créer**

### 5. Créer une Clé API
1. Dans **"Credentials"**, cliquez sur **"+ CREATE CREDENTIALS" > "API key"**
2. **Restreindre la clé** (recommandé) :
   - **Application restrictions** : HTTP referrers
   - Ajoutez : `localhost:5173/*` et `*.netlify.app/*`
   - **API restrictions** : Sélectionnez "Photos Library API"
3. **Sauvegardez**

### 6. Mettre à Jour le Fichier .env.local
Remplacez les valeurs dans `.env.local` par vos vraies clés :

```env
# Remplacez par votre vraie clé API
VITE_GOOGLE_API_KEY=AIzaSy...VotreVraieClé...

# Remplacez par votre vrai Client ID OAuth 2.0
VITE_GOOGLE_CLIENT_ID=1040083472841-...VotreVraiClientID....apps.googleusercontent.com

# Scope (ne pas modifier)
VITE_GOOGLE_PHOTOS_SCOPE=https://www.googleapis.com/auth/photoslibrary.readonly
```

## Vérifications Importantes

### ✅ Checklist de Configuration
- [ ] API Photos Library activée
- [ ] Écran de consentement OAuth configuré
- [ ] Scope `photoslibrary.readonly` ajouté
- [ ] Votre email ajouté comme utilisateur test
- [ ] Client ID OAuth 2.0 créé avec les bonnes origines
- [ ] Clé API créée et restreinte
- [ ] Fichier `.env.local` mis à jour avec les vraies clés

### 🔍 Test de Configuration
Après avoir suivi ces étapes :
1. Redémarrez votre serveur de développement (`npm run dev`)
2. Rechargez la page
3. Testez la connexion Google Photos

## Problèmes Courants

### Erreur "App not verified"
- Normal pour les apps en développement
- Cliquez sur "Advanced" puis "Go to FloraSynth (unsafe)"
- Pour la production, soumettez votre app pour vérification Google

### Erreur 403 persistante
- Vérifiez que l'API est bien activée (peut prendre 10-15 minutes)
- Vérifiez que votre email est dans les utilisateurs test
- Vérifiez que les origines JavaScript sont correctes

### Token invalide
- Effacez le cache du navigateur
- Déconnectez-vous et reconnectez-vous

## Support
Si l'erreur 403 persiste après ces étapes, vérifiez :
1. Les logs de la console Google Cloud
2. Les quotas de l'API Photos Library
3. Les restrictions de votre compte Google
