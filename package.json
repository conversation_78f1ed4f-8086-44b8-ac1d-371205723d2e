{"name": "florasynth", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port 3000", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@google/genai": "^1.9.0", "@heroicons/react": "^2.2.0", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@vitejs/plugin-react": "^4.7.0", "browser-image-compression": "^2.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "firebase": "^11.10.0", "framer-motion": "^12.23.5", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.3", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@types/node": "^22.14.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "typescript": "~5.7.2", "vite": "^6.2.0"}}