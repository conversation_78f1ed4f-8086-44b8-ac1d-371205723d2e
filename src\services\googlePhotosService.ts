import { auth } from './api';

// Interface pour les photos Google Photos
export interface GooglePhoto {
  id: string;
  baseUrl: string;
  filename: string;
  mediaMetadata: {
    creationTime: string;
    width: string;
    height: string;
  };
  mimeType: string;
}

// Interface pour la réponse de l'API Google Photos
interface GooglePhotosResponse {
  mediaItems: GooglePhoto[];
  nextPageToken?: string;
}

/**
 * Service pour interagir avec l'API Google Photos
 * Utilise UNIQUEMENT Firebase Authentication
 */
export class GooglePhotosService {
  private static readonly BASE_URL = 'https://photoslibrary.googleapis.com/v1';

  /**
   * Obtenir le token d'accès Firebase pour Google Photos
   */
  private static async getAccessToken(): Promise<string | null> {
    const user = auth.currentUser;
    if (!user) {
      console.error('❌ Utilisateur non connecté à Firebase');
      return null;
    }

    try {
      // Obtenir le token d'accès Firebase
      const token = await user.getIdToken();
      console.log('✅ Token Firebase obtenu');
      return token;
    } catch (error) {
      console.error('❌ Erreur lors de l\'obtention du token:', error);
      return null;
    }
  }

  /**
   * Vérifier si l'utilisateur est connecté à Firebase
   */
  static async isSignedIn(): Promise<boolean> {
    const user = auth.currentUser;
    return user !== null;
  }

  /**
   * Récupérer les photos récentes avec Firebase Auth
   */
  static async getRecentPhotos(pageSize: number = 20, hoursBack: number = 24): Promise<GooglePhoto[]> {
    console.log('🔍 Récupération des photos Google Photos via Firebase...');

    // Vérifier la connexion Firebase
    if (!await this.isSignedIn()) {
      throw new Error('Utilisateur non connecté à Firebase');
    }

    // Obtenir le token Firebase
    const token = await this.getAccessToken();
    if (!token) {
      throw new Error('Impossible d\'obtenir le token Firebase');
    }

    try {
      // Appel API Google Photos avec token Firebase
      const response = await fetch(`${this.BASE_URL}/mediaItems?pageSize=${pageSize}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        console.error(`❌ Erreur API: ${response.status} ${response.statusText}`);
        const errorText = await response.text();
        console.error('Détails:', errorText);
        throw new Error(`Erreur API Google Photos: ${response.status}`);
      }

      const data: GooglePhotosResponse = await response.json();
      const mediaItems = data.mediaItems || [];

      console.log(`✅ ${mediaItems.length} photos récupérées`);

      // Filtrer les photos récentes
      const cutoffTime = new Date();
      cutoffTime.setHours(cutoffTime.getHours() - hoursBack);

      const recentPhotos = mediaItems.filter(item => {
        const creationTime = new Date(item.mediaMetadata.creationTime);
        return creationTime >= cutoffTime;
      });

      console.log(`📅 ${recentPhotos.length} photos récentes (dernières ${hoursBack}h)`);

      return recentPhotos.map(item => ({
        id: item.id,
        baseUrl: item.baseUrl,
        filename: item.filename || `photo_${item.id}.jpg`,
        mediaMetadata: {
          creationTime: item.mediaMetadata.creationTime,
          width: item.mediaMetadata.width || '0',
          height: item.mediaMetadata.height || '0'
        },
        mimeType: item.mimeType || 'image/jpeg'
      }));

    } catch (error) {
      console.error('❌ Erreur lors de la récupération des photos:', error);
      throw error;
    }
  }

  /**
   * Récupérer toutes les photos avec Firebase Auth
   */
  static async getAllPhotos(pageSize: number = 50): Promise<GooglePhoto[]> {
    console.log('🔍 Récupération de toutes les photos via Firebase...');

    // Vérifier la connexion Firebase
    if (!await this.isSignedIn()) {
      throw new Error('Utilisateur non connecté à Firebase');
    }

    // Obtenir le token Firebase
    const token = await this.getAccessToken();
    if (!token) {
      throw new Error('Impossible d\'obtenir le token Firebase');
    }

    try {
      // Appel API Google Photos avec token Firebase
      const response = await fetch(`${this.BASE_URL}/mediaItems?pageSize=${pageSize}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        console.error(`❌ Erreur API: ${response.status} ${response.statusText}`);
        const errorText = await response.text();
        console.error('Détails:', errorText);
        throw new Error(`Erreur API Google Photos: ${response.status}`);
      }

      const data: GooglePhotosResponse = await response.json();
      const mediaItems = data.mediaItems || [];

      console.log(`✅ ${mediaItems.length} photos récupérées`);

      return mediaItems.map(item => ({
        id: item.id,
        baseUrl: item.baseUrl,
        filename: item.filename || `photo_${item.id}.jpg`,
        mediaMetadata: {
          creationTime: item.mediaMetadata.creationTime,
          width: item.mediaMetadata.width || '0',
          height: item.mediaMetadata.height || '0'
        },
        mimeType: item.mimeType || 'image/jpeg'
      }));

    } catch (error) {
      console.error('❌ Erreur lors de la récupération des photos:', error);
      throw error;
    }
  }

  /**
   * Récupérer les photos par type (utilise getAllPhotos)
   */
  static async getPhotosByType(searchTerms: string[] = ['plant', 'garden', 'flower'], pageSize: number = 30): Promise<GooglePhoto[]> {
    // Pour l'instant, retourner toutes les photos
    return this.getAllPhotos(pageSize);
  }
}
