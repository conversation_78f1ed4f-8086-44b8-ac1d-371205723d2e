
**Toujours vérifier ta mémoire et vérifier ensuite ce fichier. On ne sait jamais, je peux en cours de route te mettre de nouvelles instructions lorsque toi tu travailles.pas de fichier MD à la racine. Le seul fichier MD qui existe, c'est le mien : `cisco_demande.md`** Ça, c'est mon fichier personnel. Tu n'écris rien du tout dedans. Tu ne rédiges absolument rien du tout. C'est mon fichier à moi, celui-là. 



Vous avez mon approbation. 

Attention, vous mettez en place, c'est très bien, l'authentification Google Photos avec Firebase qui, elle, n'a pas besoin d'avoir toutes ces permissions comme tout à l'heure, mais par contre, l'ancien code, est-ce que vous avez pensé à le nettoyer ? Veuillez vérifier qu'il n'y ait pas de conflit ou des futurs conflits. Parce que si vous ne nettoyez pas correctement, ça risque de créer des conflits. Méfiez-vous ! 

Vous avez mis en place un mode démo, vous penserez bien à l'enlever pour passer au mode direct. Pour que je puisse récupérer mes photos sur mon espace Google Photos. 


Attention, tous les anciens fichiers de code qui ne sont plus utilisés. Vérifiez même deux fois s'il le faut. Mais supprimez-les si on n'a plus l'utilité de ces fichiers de code, surtout pour l'implémentation de Google Photos, parce qu'on avait mis en place d'autres solutions avant. Maintenant, avec la nouvelle, il ne faut garder que la nouvelle qui fonctionne et tout l'ancien code, il faut le supprimer. 










