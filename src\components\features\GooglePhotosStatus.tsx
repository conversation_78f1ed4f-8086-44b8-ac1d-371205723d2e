import React, { useState, useEffect } from 'react';
import { GooglePhotosService } from '../../services/googlePhotosService';
import { useAuth } from '../../hooks/useAuth';
import { signInWithGoogle } from '../../services/api';

interface GooglePhotosStatusProps {
  onStatusChange?: (isConnected: boolean) => void;
}

/**
 * Composant pour afficher le statut de connexion à Google Photos
 */
export const GooglePhotosStatus: React.FC<GooglePhotosStatusProps> = ({ onStatusChange }) => {
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasPhotosPermission, setHasPhotosPermission] = useState(false);
  const { user } = useAuth();

  const checkStatus = async () => {
    if (!user) {
      setIsConnected(false);
      setHasPhotosPermission(false);
      setError('Utilisateur non connecté à Firebase');
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // Initialiser le service
      await GooglePhotosService.initialize();

      // Vérifier le statut de connexion Firebase
      const connected = await GooglePhotosService.isSignedIn();
      setIsConnected(connected);

      // Vérifier les permissions Google Photos
      if (connected) {
        const hasPermission = await GooglePhotosService.hasPhotosPermission();
        setHasPhotosPermission(hasPermission);
        onStatusChange?.(hasPermission);
      } else {
        setHasPhotosPermission(false);
        onStatusChange?.(false);
      }

    } catch (error) {
      console.error('Erreur lors de la vérification du statut:', error);
      setError(error instanceof Error ? error.message : 'Erreur inconnue');
      setIsConnected(false);
      setHasPhotosPermission(false);
      onStatusChange?.(false);
    } finally {
      setIsLoading(false);
    }
  };

  // Vérifier le statut au montage et quand l'utilisateur change
  useEffect(() => {
    checkStatus();
  }, [user]);

  const handleConnect = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Si l'utilisateur n'est pas connecté à Firebase, le connecter d'abord
      if (!user) {
        console.log('🔑 Connexion à Firebase avec Google...');
        const result = await signInWithGoogle();
        if (!result.success) {
          throw new Error(result.error || 'Échec de la connexion Firebase');
        }
        console.log('✅ Connexion Firebase réussie');
      }

      // Ensuite vérifier les permissions Google Photos
      await GooglePhotosService.signIn();
      await checkStatus();

    } catch (error) {
      console.error('Erreur lors de la connexion:', error);
      setError(error instanceof Error ? error.message : 'Erreur de connexion');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center space-x-2 p-3 bg-blue-900/20 border border-blue-500/30 rounded-lg">
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-400"></div>
        <span className="text-blue-300 text-sm">Vérification du statut Google Photos...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-3 bg-red-900/20 border border-red-500/30 rounded-lg">
        <div className="flex items-center space-x-2">
          <span className="text-red-300 text-sm">❌ Erreur: {error}</span>
        </div>
        <button
          onClick={checkStatus}
          className="mt-2 px-3 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700 transition-colors"
        >
          Réessayer
        </button>
      </div>
    );
  }

  // Si connecté à Firebase ET a les permissions Google Photos
  if (isConnected && hasPhotosPermission) {
    return (
      <div className="p-3 bg-green-900/20 border border-green-500/30 rounded-lg">
        <div className="flex items-center space-x-2">
          <span className="text-green-300 text-sm">✅ Connecté à Google Photos</span>
        </div>
        <button
          onClick={checkStatus}
          className="mt-2 px-3 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700 transition-colors"
        >
          Actualiser
        </button>
      </div>
    );
  }

  // Sinon, afficher le bouton de connexion unique
  return (
    <div className="p-3 bg-yellow-900/20 border border-yellow-500/30 rounded-lg">
      <div className="flex items-center space-x-2">
        <span className="text-yellow-300 text-sm">
          {!user ? '⚠️ Connexion Firebase requise' : '⚠️ Permissions Google Photos manquantes'}
        </span>
      </div>
      <button
        onClick={handleConnect}
        className="mt-2 px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white text-sm rounded-lg hover:from-purple-700 hover:to-pink-700 transition-all duration-200 flex items-center space-x-2"
        disabled={isLoading}
      >
        <svg className="w-4 h-4" viewBox="0 0 24 24" fill="currentColor">
          <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
          <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
          <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
          <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
        </svg>
        <span>Se connecter à Google Photos</span>
      </button>
    </div>
  );
};
