import React, { useState, useEffect } from 'react';
import { GooglePhotosService } from '../../services/googlePhotosService';
import { useAuth } from '../../hooks/useAuth';

interface GooglePhotosStatusProps {
  onStatusChange?: (isConnected: boolean) => void;
}

/**
 * Composant pour afficher le statut de connexion à Google Photos
 */
export const GooglePhotosStatus: React.FC<GooglePhotosStatusProps> = ({ onStatusChange }) => {
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();

  const checkStatus = async () => {
    if (!user) {
      setIsConnected(false);
      setError('Utilisateur non connecté à Firebase');
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // Initialiser le service
      await GooglePhotosService.initialize();

      // Vérifier le statut de connexion
      const connected = await GooglePhotosService.isSignedIn();
      setIsConnected(connected);

      // Notifier le parent du changement de statut
      onStatusChange?.(connected);

    } catch (error) {
      console.error('Erreur lors de la vérification du statut:', error);
      setError(error instanceof Error ? error.message : 'Erreur inconnue');
      setIsConnected(false);
      onStatusChange?.(false);
    } finally {
      setIsLoading(false);
    }
  };

  // Vérifier le statut au montage et quand l'utilisateur change
  useEffect(() => {
    checkStatus();
  }, [user]);

  const handleConnect = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      await GooglePhotosService.signIn();
      await checkStatus();
      
    } catch (error) {
      console.error('Erreur lors de la connexion:', error);
      setError(error instanceof Error ? error.message : 'Erreur de connexion');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center space-x-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
        <span className="text-blue-700 text-sm">Vérification du statut Google Photos...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
        <div className="flex items-center space-x-2">
          <span className="text-red-600 text-sm">❌ Erreur: {error}</span>
        </div>
        <button
          onClick={checkStatus}
          className="mt-2 px-3 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700 transition-colors"
        >
          Réessayer
        </button>
      </div>
    );
  }

  if (isConnected) {
    return (
      <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
        <div className="flex items-center space-x-2">
          <span className="text-green-600 text-sm">✅ Connecté à Google Photos</span>
        </div>
        <button
          onClick={checkStatus}
          className="mt-2 px-3 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700 transition-colors"
        >
          Actualiser
        </button>
      </div>
    );
  }

  return (
    <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
      <div className="flex items-center space-x-2">
        <span className="text-yellow-700 text-sm">⚠️ Non connecté à Google Photos</span>
      </div>
      <button
        onClick={handleConnect}
        className="mt-2 px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700 transition-colors"
      >
        Se connecter
      </button>
    </div>
  );
};
